name: Reassure Performance Tests

on:
  pull_request:
    types: [opened, synchronize]
    branches-ignore: [staging, production]
    paths-ignore: [docs/**, help/**, .github/**, contributingGuides/**, tests/**, '**.md', '**.sh']

jobs:
  perf-tests:
    if: ${{ github.actor != 'OSBotify' }}
    runs-on: ubuntu-24.04-v4
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608
        with:
          fetch-depth: 0

      - name: Setup NodeJS
        uses: ./.github/actions/composite/setupNode

      - name: Set dummy git credentials
        run: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "Test"

      - name: Get common ancestor commit
        run: |
            git fetch origin main
            common_ancestor=$(git merge-base "${{ github.sha }}" origin/main)
            echo "COMMIT_HASH=$common_ancestor" >> "$GITHUB_ENV"

      - name: Clean up deleted files
        run: |
            DELETED_FILES=$(git diff --name-only --diff-filter=D "$COMMIT_HASH" "${{ github.sha }}")
            for file in $DELETED_FILES; do
              if [ -n "$file" ]; then
                rm -f "$file"
                echo "Deleted file: $file"
              fi
            done

      - name: Run performance testing script
        shell: bash
        run: |
          set -e
          BASELINE_BRANCH=${BASELINE_BRANCH:="main"}
          git fetch origin "$BASELINE_BRANCH" --no-tags --depth=1
          git switch "$BASELINE_BRANCH"
          cd Mobile-Expensify
          git checkout "$(git rev-parse HEAD)"
          cd ..
          git add Mobile-Expensify
          npm install --force || (rm -rf node_modules && npm install --force) 
          NODE_OPTIONS=--experimental-vm-modules npx reassure --baseline
          git switch --force --detach -
          cd Mobile-Expensify
          git checkout "$(git rev-parse HEAD)"
          cd ..
          git add Mobile-Expensify
          git merge --no-commit --allow-unrelated-histories "$BASELINE_BRANCH" -X ours
          git checkout --ours .
          npm install --force || (rm -rf node_modules && npm install --force)
          NODE_OPTIONS=--experimental-vm-modules npx reassure --branch

      - name: Validate output.json
        id: validateReassureOutput
        uses: ./.github/actions/javascript/validateReassureOutput
        with:
          DURATION_DEVIATION_PERCENTAGE: 20
          COUNT_DEVIATION: 0
