---
title: Search and Download Expenses
description: Learn how to find expenses in New Expensify and export them as a CSV or PDF.
keywords: [New Expensify, search expenses, download expenses, export CSV, export PDF, download report, download receipt]
---
<div id="new-expensify" markdown="1">

You can quickly find and download your expenses in New Expensify—either as a spreadsheet-ready CSV or a printable PDF.

---

# Search for Expenses

Use filters to quickly locate the expenses you need.

1. Click **Reports** in the left-hand menu
2. Select **Expenses**
3. Use **Filters** to search for expenses by:
   - Credit card
   - Category or tag
   - Date range
   - Keyword
   - Expense amount
   - And more
4. Click **View results** to display the list of expenses matching your search parameters

---

# Download Expenses as a CSV

Once you’ve filtered the list:

1. Select the checkbox beside each expense, or choose to **select all** above the expense list.
2. Click **Selected > Download**.
3. A CSV file will automatically download to your device (prefixed with “Expensify”).
4. Open it in your spreadsheet tool.

**Included in the CSV:**
- Date  
- Merchant  
- Description  
- From  
- To  
- Category + GL code  
- Tag + GL code  
- Tax + Tax code  
- Amount  
- Currency  
- Type (e.g., cash, card, distance)  
- Receipt URL

---

# Download a Report (PDF or CSV)

1. In the left-hand menu, select **Reports**
2. On the **Reports** tab, click the report you want to download
3. In the upper-right corner, click **More**
4. Choose either:
   - **Download as PDF** to generate a printable version
   - **Download as CSV** to get a spreadsheet version of the report

**Note:** You can only download one report at a time.

---

# FAQ

## Can I export in PDF or XLS format?

You can export:
- CSV for raw expense data
- PDF for full reports

XLS format is not supported.

## Is it possible to download individual expenses as a PDF?

No, you can only download a PDF of full reports. 

## Can I customize the columns in the CSV file?

No, the CSV download uses a fixed template and cannot be customized.

## Can I select expenses or reports in bulk?

Yes! Use **Select multiple** or **Select all** to choose multiple expenses for CSV export.  
PDFs must be downloaded one at a time.

## Why do I see a 404 error when clicking the receipt URL in the CSV?

Make sure you're logged into your Expensify account in the same browser. Receipt links only work when you're signed in.

</div>
