---
title: Copilot Access
description: How to add a Copilot or act as a Copilot for delegated account access
keywords: [New Expensify, copilot, delegated access, switch accounts, expense management, account assistant, view user account, expense forwarding]
---
<div id="new-expensify" markdown="1">

You can assign someone access to your Expensify account as a **Copilot**. A Copilot can help manage expenses, review reports, and access your settings without sharing your login details.

---

# What Can a Copilot Do?

Copilots can access another user’s Expensify account to:

- View all expenses and chats
- Submit and manage expenses on behalf of the account owner
- Approve and reimburse reports
- Access domain and workspace settings

**Copilots cannot:**

- Add or remove other Copilots
- Take actions restricted by their access level

---

# Add a Copilot

To grant someone access to your account:

1. In the left-hand menu, click **Account > Security**.
2. In the **Copilot: Delegated Access** section, click **Add Copilot**.
3. Search for the user by **name** or **email**.
4. Choose the access level:
   - **Full Access** – Grants complete access to all areas and actions.
   - **Limited Access** – Excludes approvals, payments, rejections, and holds.
5. Click **Add Copilot**.

💡 **Note:** You can invite someone who doesn’t have an Expensify account. They’ll receive an invite to create one.

---

# Remove or Change a Copilot’s Access

To **remove a Copilot**:

1. In the left-hand menu, click **Account > Security**.
2. In the **Copilot: Delegated Access** section, click the **three-dot menu** next to their name.
3. Select **Remove Copilot**, then confirm.

To **change a Copilot's access level**:

1. In the left-hand menu, click **Account > Security**.
2. In the **Copilot: Delegated Access** section, click the **three-dot menu** next to their name.
3. Choose **Change Access Level**.
4. Select **Full Access** or **Limited Access** and confirm your choice.

---

# Switch to a Copilot Account

If you've been added as a Copilot, you can switch into the other user’s account easily.

1. Navigate to the **Account** tab
2. Click your name or profile icon to access a drop-down that lists the accounts you have Copilot access to
3. Select the account you want to access

You'll see a **dual avatar** in the bottom-left corner confirming you're in Copilot mode.

---

# Forward Receipts as a Copilot

To send a receipt to the account you support:

1. Forward the email to **<EMAIL>**.
2. In the subject line, include the **email address of the user** you're a Copilot for.

This routes the receipt to their account automatically.

---

# FAQ

## Can a Copilot Add or Remove Other Copilots?

No. Copilots can’t add or remove others. Only the account owner can manage Copilots, though a Copilot can remove themselves.

## How Are Copilot Actions Tracked?

Actions taken by a Copilot are clearly labeled as performed “on behalf of” the account owner in Expensify.

## Can I Have Multiple Copilots?

Yes! You can add as many Copilots as you like. (You just need to wait one minute between additions.)

</div>
